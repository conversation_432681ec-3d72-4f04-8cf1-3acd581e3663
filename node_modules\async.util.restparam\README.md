# async.util.restparam

![Last version](https://img.shields.io/github/tag/async-js/async.util.restparam.svg?style=flat-square)
[![Dependency status](http://img.shields.io/david/async-js/async.util.restparam.svg?style=flat-square)](https://david-dm.org/async-js/async.util.restparam)
[![Dev Dependencies Status](http://img.shields.io/david/dev/async-js/async.util.restparam.svg?style=flat-square)](https://david-dm.org/async-js/async.util.restparam#info=devDependencies)
[![NPM Status](http://img.shields.io/npm/dm/async.util.restparam.svg?style=flat-square)](https://www.npmjs.org/package/async.util.restparam)
[![Donate](https://img.shields.io/badge/donate-paypal-blue.svg?style=flat-square)](https://paypal.me/kikobeats)

> async restparam helper method as module.

This module is used internally by [async](https://github.com/async-js/async).

## License

MIT © [async-js](https://github.com/async-js)
