const { ActionRow<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bedB<PERSON><PERSON>, StringSelectMenuBuilder } = require('discord.js');

// Botones principales de control de música
const createMusicControlButtons = (queue) => {
    const row1 = new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setCustomId('pause_resume')
                .setLabel(queue?.paused ? '▶️ Reanudar' : '⏸️ Pausar')
                .setStyle(queue?.paused ? ButtonStyle.Success : ButtonStyle.Primary),
            new ButtonBuilder()
                .setCustomId('skip')
                .setLabel('⏭️ Siguiente')
                .setStyle(ButtonStyle.Secondary),
            new ButtonBuilder()
                .setCustomId('stop')
                .setLabel('⏹️ Detener')
                .setStyle(ButtonStyle.Danger),
            new ButtonBuilder()
                .setCustomId('queue')
                .setLabel('📑 Cola')
                .setStyle(ButtonStyle.Secondary),
            new ButtonBuilder()
                .setCustomId('shuffle')
                .setLabel('🔀 Mezclar')
                .setStyle(ButtonStyle.Secondary)
        );

    return [row1];
};

// Botones de volumen y configuración
const createVolumeControlButtons = (queue) => {
    const row2 = new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setCustomId('volume_down')
                .setLabel('🔉 -10')
                .setStyle(ButtonStyle.Secondary),
            new ButtonBuilder()
                .setCustomId('volume_up')
                .setLabel('🔊 +10')
                .setStyle(ButtonStyle.Secondary),
            new ButtonBuilder()
                .setCustomId('repeat')
                .setLabel(getRepeatLabel(queue?.repeatMode))
                .setStyle(getRepeatStyle(queue?.repeatMode)),
            new ButtonBuilder()
                .setCustomId('autoplay')
                .setLabel(queue?.autoplay ? '🔄 Auto: ON' : '🔄 Auto: OFF')
                .setStyle(queue?.autoplay ? ButtonStyle.Success : ButtonStyle.Secondary),
            new ButtonBuilder()
                .setCustomId('lyrics')
                .setLabel('📝 Letra')
                .setStyle(ButtonStyle.Secondary)
        );

    return row2;
};

// Función helper para obtener el label del repeat
function getRepeatLabel(repeatMode) {
    switch (repeatMode) {
        case 0: return '🔁 Repetir: OFF';
        case 1: return '🔂 Repetir: Canción';
        case 2: return '🔁 Repetir: Cola';
        default: return '🔁 Repetir: OFF';
    }
}

// Función helper para obtener el estilo del repeat
function getRepeatStyle(repeatMode) {
    return repeatMode > 0 ? ButtonStyle.Success : ButtonStyle.Secondary;
}

// Embed mejorado para la canción actual
const createNowPlayingEmbed = (song, queue) => {
    const progressBar = createProgressBar(queue);

    // Obtener información adicional
    const nextSong = queue?.songs?.[1];
    const queueLength = queue?.songs?.length || 0;

    const embed = new EmbedBuilder()
        .setTitle('🎵 Reproduciendo ahora')
        .setDescription(`**[${song.name}](${song.url})**\n${song.uploader?.name ? `*Por: ${song.uploader.name}*` : ''}`)
        .setThumbnail(song.thumbnail)
        .addFields(
            { name: '👤 Solicitado por', value: `<@${song.user.id}>`, inline: true },
            { name: '⏱️ Duración', value: song.formattedDuration, inline: true },
            { name: '🔊 Volumen', value: `${queue?.volume || 100}%`, inline: true },
            { name: '📊 Progreso', value: progressBar, inline: false },
            { name: '🎛️ Estado', value: getQueueStatus(queue), inline: true },
            { name: '📑 Cola', value: queueLength > 1 ? `${queueLength - 1} canciones en espera` : 'Sin canciones en cola', inline: true }
        )
        .setColor(queue?.paused ? '#FFA500' : '#00FF7F') // Naranja si pausado, verde si reproduciendo
        .setTimestamp();

    // Agregar información de la siguiente canción si existe
    if (nextSong) {
        embed.addFields({
            name: '⏭️ Siguiente',
            value: `[${nextSong.name.length > 50 ? nextSong.name.substring(0, 47) + '...' : nextSong.name}](${nextSong.url})`,
            inline: false
        });
    }

    embed.setFooter({
        text: `Filtros: ${queue?.filters?.names?.join(', ') || 'Ninguno'} | Panel actualizado`,
        iconURL: song.user.displayAvatarURL()
    });

    return embed;
};

// Crear barra de progreso visual mejorada con tiempo real
function createProgressBar(queue) {
    if (!queue || !queue.songs[0]) return '▱▱▱▱▱▱▱▱▱▱▱▱ 0%\n`0:00 / 0:00`';

    // Calcular progreso basado en tiempo real
    const song = queue.songs[0];
    const startTime = queue.beginTime || Date.now();
    const elapsed = Math.floor((Date.now() - startTime) / 1000);
    const current = queue.paused ? queue.currentTime : Math.min(elapsed, song.duration);
    const total = song.duration;

    // Evitar división por cero
    if (total <= 0) return '▱▱▱▱▱▱▱▱▱▱▱▱ 0%\n`0:00 / 0:00`';

    const progress = Math.min(Math.round((current / total) * 100), 100);
    const progressBarLength = 12; // Barra más larga para mejor precisión
    const filledLength = Math.round((progress / 100) * progressBarLength);

    // Usar diferentes caracteres para mejor visualización
    const progressBar = '🟩'.repeat(filledLength) + '⬜'.repeat(progressBarLength - filledLength);
    const currentFormatted = formatTime(current);
    const totalFormatted = song.formattedDuration;

    // Calcular tiempo restante
    const remaining = Math.max(0, total - current);
    const remainingFormatted = formatTime(remaining);

    return `${progressBar} **${progress}%**\n\`⏱️ ${currentFormatted} / ${totalFormatted}\` • \`⏳ ${remainingFormatted} restante\``;
}

// Formatear tiempo en segundos a mm:ss
function formatTime(seconds) {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
}

// Obtener estado de la cola
function getQueueStatus(queue) {
    if (!queue) return 'No hay cola activa';

    const status = [];
    if (queue.paused) status.push('⏸️ Pausado');
    if (queue.repeatMode === 1) status.push('🔂 Repetir canción');
    if (queue.repeatMode === 2) status.push('🔁 Repetir cola');
    if (queue.autoplay) status.push('🔄 Autoplay activo');
    if (queue.filters?.names?.length > 0) status.push(`🎛️ Filtros: ${queue.filters.names.join(', ')}`);

    return status.length > 0 ? status.join(' • ') : '▶️ Reproduciendo normalmente';
}

// Embed mejorado para la cola de reproducción
const createQueueEmbed = (queue, page = 0) => {
    if (!queue || !queue.songs.length) {
        return new EmbedBuilder()
            .setTitle('📑 Cola de reproducción')
            .setDescription('❌ No hay canciones en la cola')
            .setColor('#FF6B6B');
    }

    const songsPerPage = 10;
    const start = page * songsPerPage;
    const end = start + songsPerPage;
    const songs = queue.songs.slice(start, end);
    const totalPages = Math.ceil(queue.songs.length / songsPerPage);

    const embed = new EmbedBuilder()
        .setTitle('📑 Cola de reproducción')
        .setColor('#4ECDC4')
        .setTimestamp();

    // Descripción de las canciones
    const songList = songs.map((song, i) => {
        const index = start + i;
        const emoji = index === 0 ? '🎵' : `\`${index}.\``;
        const status = index === 0 ? '**[Reproduciendo]**' : '';
        const user = `<@${song.user.id}>`;
        return `${emoji} **[${song.name}](${song.url})** ${status}\n⏱️ \`${song.formattedDuration}\` • 👤 ${user}`;
    }).join('\n\n');

    embed.setDescription(songList || 'No hay canciones en esta página');

    // Información adicional
    const totalDuration = queue.songs.reduce((acc, song) => acc + song.duration, 0);
    const totalFormatted = formatTime(totalDuration);

    embed.addFields(
        {
            name: '📊 Estadísticas',
            value: `**Total:** ${queue.songs.length} canciones\n**Duración:** ${totalFormatted}\n**Página:** ${page + 1}/${totalPages}`,
            inline: true
        },
        {
            name: '🎛️ Configuración',
            value: `**Volumen:** ${queue.volume}%\n**Repetir:** ${getRepeatLabel(queue.repeatMode).replace('🔁 ', '').replace('🔂 ', '')}\n**Autoplay:** ${queue.autoplay ? 'Activado' : 'Desactivado'}`,
            inline: true
        }
    );

    if (totalPages > 1) {
        embed.setFooter({ text: `Página ${page + 1} de ${totalPages} • Usa los botones para navegar` });
    }

    return embed;
};

// Botones de navegación para la cola
const createQueueNavigationButtons = (currentPage, totalPages) => {
    const row = new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setCustomId('queue_first')
                .setLabel('⏮️')
                .setStyle(ButtonStyle.Secondary)
                .setDisabled(currentPage === 0),
            new ButtonBuilder()
                .setCustomId('queue_prev')
                .setLabel('◀️')
                .setStyle(ButtonStyle.Secondary)
                .setDisabled(currentPage === 0),
            new ButtonBuilder()
                .setCustomId('queue_refresh')
                .setLabel('🔄 Actualizar')
                .setStyle(ButtonStyle.Primary),
            new ButtonBuilder()
                .setCustomId('queue_next')
                .setLabel('▶️')
                .setStyle(ButtonStyle.Secondary)
                .setDisabled(currentPage >= totalPages - 1),
            new ButtonBuilder()
                .setCustomId('queue_last')
                .setLabel('⏭️')
                .setStyle(ButtonStyle.Secondary)
                .setDisabled(currentPage >= totalPages - 1)
        );

    return row;
};

// Menú de selección para filtros de audio
const createFiltersSelectMenu = () => {
    return new StringSelectMenuBuilder()
        .setCustomId('audio_filters')
        .setPlaceholder('🎛️ Selecciona un filtro de audio')
        .addOptions([
            {
                label: 'Sin filtros',
                description: 'Quitar todos los filtros',
                value: 'clear',
                emoji: '🔄'
            },
            {
                label: 'Bass Boost',
                description: 'Aumentar los graves',
                value: 'bassboost',
                emoji: '🔊'
            },
            {
                label: '8D Audio',
                description: 'Efecto de audio envolvente',
                value: '8d',
                emoji: '🌀'
            },
            {
                label: 'Nightcore',
                description: 'Velocidad y tono más altos',
                value: 'nightcore',
                emoji: '⚡'
            },
            {
                label: 'Vaporwave',
                description: 'Velocidad y tono más bajos',
                value: 'vaporwave',
                emoji: '🌊'
            }
        ]);
};

// Crear panel de control completo
const createFullControlPanel = (queue) => {
    const mainButtons = createMusicControlButtons(queue);
    const volumeButtons = createVolumeControlButtons(queue);
    const filtersMenu = new ActionRowBuilder().addComponents(createFiltersSelectMenu());

    return [...mainButtons, volumeButtons, filtersMenu];
};

// Embed para mostrar información del servidor/bot
const createBotInfoEmbed = (client) => {
    return new EmbedBuilder()
        .setTitle('🤖 Información de Rosy')
        .setDescription('Bot de música avanzado para Discord')
        .setThumbnail(client.user.displayAvatarURL())
        .addFields(
            { name: '📊 Estadísticas', value: `**Servidores:** ${client.guilds.cache.size}\n**Usuarios:** ${client.users.cache.size}`, inline: true },
            { name: '🎵 Características', value: '• Slash Commands\n• Controles interactivos\n• Múltiples fuentes\n• Filtros de audio', inline: true },
            { name: '🔗 Enlaces', value: '[Invitar Bot](https://discord.com/api/oauth2/authorize?client_id=YOUR_CLIENT_ID&permissions=8&scope=bot%20applications.commands)', inline: false }
        )
        .setColor('#45B7D1')
        .setTimestamp();
};

module.exports = {
    createMusicControlButtons,
    createVolumeControlButtons,
    createNowPlayingEmbed,
    createQueueEmbed,
    createQueueNavigationButtons,
    createFiltersSelectMenu,
    createFullControlPanel,
    createBotInfoEmbed,
    formatTime,
    getQueueStatus
};
