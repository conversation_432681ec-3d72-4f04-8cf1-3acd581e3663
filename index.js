require('dotenv').config();
const { Client, GatewayIntentBits, Collection, REST, Routes } = require('discord.js');
const { DisTube } = require('distube');
const { SpotifyPlugin } = require('@distube/spotify');
const { YouTubePlugin } = require('@distube/youtube');
const { generateDependencyReport } = require('@discordjs/voice');
const PersistentPanel = require('./utils/persistentPanel');
const fs = require('fs');
const path = require('path');
require('events').EventEmitter.defaultMaxListeners = 15;

// Manejar errores no capturados
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
});

const play = require('play-dl');

// Configurar play-dl con la clave de API
if (process.env.YOUTUBE_API_KEY) {
    play.setToken('youtube', process.env.YOUTUBE_API_KEY); // Corregido el uso de setToken
    console.log('Play-dl configurado con la clave de API de YouTube.');
} else {
    console.error('YOUTUBE_API_KEY no está definido en el archivo .env.');
}

const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildVoiceStates, // Añadido GuildVoiceStates
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent
    ]
});

// Log del reporte de dependencias
console.log('Voice Dependency Report:', generateDependencyReport());

client.commands = new Collection();

// Crear instancia del panel persistente
client.persistentPanel = new PersistentPanel(client);

// Crear instancia de DisTube con plugins oficiales y configuración válida
client.distube = new DisTube(client, {
    emitNewSongOnly: false,
    emitAddSongWhenCreatingQueue: true,
    emitAddListWhenCreatingQueue: true,
    plugins: [
        new YouTubePlugin({
            // Configuración más estable del plugin oficial de YouTube
            cookies: [], // Opcional: cookies para evitar limitaciones
            ytdlOptions: {
                quality: 'highestaudio',
                filter: 'audioonly',
                highWaterMark: 1024 * 1024 * 32, // Reducido para mayor estabilidad
                requestOptions: {
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                    }
                }
            }
        }),
        new SpotifyPlugin({
            api: {
                clientId: process.env.SPOTIFY_CLIENT_ID,
                clientSecret: process.env.SPOTIFY_CLIENT_SECRET
            }
        })
    ]
});

// ✅ Función para limpiar URLs de YouTube
client.limpiarUrlYoutube = function(url) {
    try {
        // Verificar si es una URL de YouTube
        if (!url.includes('youtube.com') && !url.includes('youtu.be')) {
            return url; // No es YouTube, devolver tal como está
        }

        const urlObj = new URL(url);
        const videoId = urlObj.searchParams.get('v');
        const listId = urlObj.searchParams.get('list');

        if (listId && !videoId) {
            // Solo playlist - formato estándar
            console.log(`🔄 Detectada playlist pura: ${listId}`);
            return `https://www.youtube.com/playlist?list=${listId}`;
        } else if (videoId && !listId) {
            // Solo video - formato estándar
            console.log(`🔄 Detectado video individual: ${videoId}`);
            return `https://www.youtube.com/watch?v=${videoId}`;
        } else if (videoId && listId) {
            // Ambos presentes - priorizar playlist para reproducir toda la lista
            console.log(`🔄 Detectados video + playlist: priorizando playlist ${listId}`);
            return `https://www.youtube.com/playlist?list=${listId}`;
        }

        return url; // Devolver URL original si no se puede procesar
    } catch (error) {
        console.error('Error limpiando URL:', error);
        return url; // En caso de error, devolver URL original
    }
};

// ✅ Función wrapper para DisTube con URL limpia
client.playWithCleanUrl = async function(distube, context, query, options) {
    try {
        console.log(`🎵 Reproduciendo con URL limpia: ${query}`);

        // Usar DisTube directamente con la URL ya limpia
        await distube.play(context.voice, query, options);

        console.log(`✅ Reproducción iniciada exitosamente`);

    } catch (error) {
        console.error(`❌ Error en playWithCleanUrl:`, error);
        throw error; // Re-lanzar el error para que lo maneje el comando
    }
};

// Antes de configurar los eventos de DisTube
client.distube.setMaxListeners(25);

// Eliminar los eventos duplicados
client.distube.removeAllListeners();

// Un solo lugar para todos los eventos de DisTube
const status = queue =>
    `Volumen: \`${queue.volume}%\` | Filtro: \`${queue.filters.names.join(', ') || 'Off'}\` | Loop: \`${
    queue.repeatMode ? (queue.repeatMode === 2 ? 'All Queue' : 'This Song') : 'Off'
    }\` | Autoplay: \`${queue.autoplay ? 'On' : 'Off'}\``;

const events = {
    playSong: (queue, song) => {
        console.log(`🎵 Evento playSong: "${song.name}" en ${queue.textChannel?.guild.name}`);
        console.log(`🔍 Estado de la cola: ${queue.songs.length} canciones | Autoplay: ${queue.autoplay} | RepeatMode: ${queue.repeatMode}`);

        // Guardar tiempo de inicio de la canción para el progreso
        queue.beginTime = Date.now();

        // Crear/actualizar panel de forma segura
        if (queue.textChannel) {
            try {
                client.persistentPanel.createOrUpdatePanel(queue.textChannel, 'playSong_event');
            } catch (panelError) {
                console.error('Error creando panel en playSong:', panelError);
            }
        }

        // NO enviar mensajes temporales para evitar spam
        // El panel ya muestra toda la información necesaria
    },
    addSong: (queue, song) => {
        console.log(`➕ Evento addSong: "${song.name}" agregada a la cola`);

        // NO enviar mensajes para evitar spam
        // El panel ya muestra la información de la cola
    },
    addList: (queue, playlist) => {
        console.log(`📑 Evento addList: "${playlist.name || 'Playlist'}" con ${playlist.songs.length} canciones`);
        console.log(`🔍 Primera canción de la playlist: "${playlist.songs[0]?.name}"`);
        console.log(`🔍 Estado después de agregar playlist: ${queue.songs.length} canciones en cola | Autoplay: ${queue.autoplay}`);

        // Enviar UN SOLO mensaje de confirmación de playlist
        queue.textChannel?.send(
            `📑 **Playlist agregada:** ${playlist.name || 'Playlist de YouTube'}\n🎵 **${playlist.songs.length} canciones** agregadas por ${playlist.user}`
        ).then(msg => {
            setTimeout(() => msg.delete().catch(() => {}), 8000);
        });
    },
    finishSong: (queue, song) => {
        console.log(`🏁 Evento finishSong: "${song?.name || 'Canción desconocida'}" terminó`);
        console.log(`🔍 Siguiente en cola: ${queue.songs.length > 0 ? queue.songs[0]?.name || 'Canción sin nombre' : 'Ninguna'}`);
        console.log(`🔍 Estado de la cola después de terminar: ${queue.songs.length} canciones | Autoplay: ${queue.autoplay}`);
    },
    finish: queue => {
        console.log(`🏁 Evento finish: Cola terminada en ${queue.textChannel?.guild.name}`);
        // El panel se eliminará automáticamente cuando no haya música
        queue.textChannel?.send('🏁 ¡Cola terminada! Usa `/play` para reproducir más música.').then(msg => {
            setTimeout(() => msg.delete().catch(() => {}), 10000);
        });
    },
    error: (channel, error) => {
        console.error('DisTube Error:', error.message);
        console.error('Error Code:', error.errorCode);

        // Si es un error de FFmpeg, intentar continuar sin mostrar mensaje al usuario
        if (error.errorCode === 'FFMPEG_EXITED') {
            console.log('🔧 Error de FFmpeg detectado, intentando continuar...');
            return; // No mostrar mensaje de error al usuario para errores de FFmpeg
        }

        // Para otros errores, mostrar mensaje al usuario
        let errorMessage = '❌ Error de reproducción.';

        if (error.message.includes('Video unavailable')) {
            errorMessage = '❌ Video no disponible.';
        } else if (error.message.includes('private')) {
            errorMessage = '❌ Video privado.';
        } else if (error.message.includes('age')) {
            errorMessage = '❌ Video con restricción de edad.';
        }

        // Verificar si channel es un canal de texto válido
        if (channel && typeof channel.send === 'function') {
            channel.send(errorMessage).then(msg => {
                setTimeout(() => msg.delete().catch(() => {}), 8000);
            }).catch(console.error);
        } else {
            // Si no hay canal válido, intentar obtener el canal de texto de la queue
            if (error.queue && error.queue.textChannel && typeof error.queue.textChannel.send === 'function') {
                error.queue.textChannel.send(errorMessage).then(msg => {
                    setTimeout(() => msg.delete().catch(() => {}), 8000);
                }).catch(console.error);
            } else {
                console.error('No se pudo enviar mensaje de error - canal no válido:', error.message);
            }
        }
    }
};

// Registrar todos los eventos una sola vez
Object.entries(events).forEach(([event, handler]) => {
    client.distube.on(event, handler);
});

const config = require('./config'); // Importar configuración desde config.js
console.log('Configuración cargada:', config);

client.commands = new Collection();
console.log('Collection de comandos inicializada');

// Función para sincronizar slash commands automáticamente
async function syncSlashCommands() {
    try {
        console.log('🔄 Sincronizando slash commands...');

        const commands = [];

        // Cargar comandos desde la carpeta commands
        function loadCommands(dir) {
            const files = fs.readdirSync(dir);

            for (const file of files) {
                const filePath = path.join(dir, file);
                const stat = fs.statSync(filePath);

                if (stat.isDirectory()) {
                    loadCommands(filePath);
                } else if (file.endsWith('.js')) {
                    try {
                        const command = require(filePath);
                        if (command.data && command.name) {
                            commands.push(command.data.toJSON());
                        }
                    } catch (error) {
                        console.error(`❌ Error cargando comando ${file}:`, error.message);
                    }
                }
            }
        }

        const commandsPath = path.join(__dirname, 'commands');
        loadCommands(commandsPath);

        // Sincronizar comandos
        const rest = new REST().setToken(process.env.DISCORD_TOKEN);
        const data = await rest.put(
            Routes.applicationCommands(process.env.CLIENT_ID),
            { body: commands }
        );

        console.log(`✅ ${data.length} slash commands sincronizados automáticamente!`);

    } catch (error) {
        console.error('❌ Error sincronizando slash commands:', error.message);
    }
}

client.on('ready', async () => {
    console.log(`¡Bot listo! Conectado como ${client.user.tag}`);

    // Cargar comandos, eventos e interacciones
    require('./handlers/commands')(client);
    require('./handlers/events')(client);
    require('./handlers/interactions')(client);

    // Mostrar todos los comandos cargados
    console.log('Comandos cargados:', [...client.commands.keys()]);

    // Sincronizar slash commands automáticamente
    await syncSlashCommands();

    // Configurar la presencia correctamente
    try {
        await client.user.setPresence({
            activities: config.presence.activities,
            status: config.presence.status
        });
        console.log('✅ Presencia actualizada correctamente');
    } catch (error) {
        console.error('Error al configurar la presencia:', error);
    }

    // Iniciar sistema de paneles persistentes mejorado
    client.persistentPanel.start();
});

// Manejar comandos tradicionales (con prefix)
client.on('messageCreate', async message => {
    if (message.author.bot) return;

    console.log('Mensaje recibido:', message.content);

    if (!message.content.startsWith(config.prefix)) {
        console.log('Mensaje no comienza con el prefix');
        return;
    }

    const args = message.content.slice(config.prefix.length).trim().split(/ +/);
    const commandName = args.shift().toLowerCase();

    console.log('Comando solicitado:', commandName);
    console.log('Argumentos:', args);

    const command = client.commands.get(commandName);
    if (!command) {
        console.log('Comando no encontrado');
        return;
    }

    try {
        console.log('Ejecutando comando:', commandName);
        await command.execute(message, args, client);
    } catch (error) {
        console.error('Error ejecutando comando:', error);
        message.reply('¡Hubo un error al ejecutar el comando!').catch(console.error);
    }
});

// Los slash commands se manejan en handlers/interactions.js

// Iniciar sesión del bot
client.login(process.env.DISCORD_TOKEN);

// Manejar errores no capturados
process.on('unhandledRejection', error => {
    console.error('Error no manejado:', error);
});