const { createNowPlayingEmbed, createFullControlPanel, createBotInfoEmbed } = require('./components');

/**
 * Sistema de panel de control persistente
 * Mantiene un mensaje con controles siempre visible y actualizado
 */

class PersistentPanel {
    constructor(client) {
        this.client = client;
        this.activePanels = new Map(); // guildId -> { channelId, messageId, lastUpdate, lastSongId, lastProgress }
        this.updateInterval = null;
        this.PANEL_UPDATE_INTERVAL = 8000; // 15 segundos - balance entre actualización y rendimiento
        this.PROGRESS_UPDATE_THRESHOLD = 10; // Solo actualizar si el progreso cambió más del 10%
    }

    /**
     * Inicia el sistema de paneles persistentes automático
     */
    start() {
        // Actualizar paneles cada 7 segundos - eliminar y recrear para mantener arriba
        this.updateInterval = setInterval(() => {
            this.refreshAllPanels();
        }, this.PANEL_UPDATE_INTERVAL);

        console.log('🎛️ Sistema de paneles persistentes iniciado (actualización inteligente cada 15 segundos)');
    }

    /**
     * Detiene el sistema de paneles persistentes
     */
    stop() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
        console.log('🎛️ Sistema de paneles persistentes detenido');
    }

    /**
     * Crea o actualiza un panel persistente con progreso en tiempo real
     * @param {TextChannel} channel - Canal donde crear el panel
     * @param {string} source - Fuente que está llamando esta función (para debugging)
     */
    async createOrUpdatePanel(channel, source = 'unknown') {
        try {
            const guildId = channel.guild.id;
            const queue = this.client.distube.getQueue(channel.guild);

            // Si no hay música, eliminar panel si existe
            if (!queue || !queue.songs.length) {
                await this.removePreviousPanel(guildId);
                return;
            }

            const currentSong = queue.songs[0];
            const existingPanel = this.activePanels.get(guildId);

            // Decidir si crear nuevo panel o actualizar existente
            if (!existingPanel || existingPanel.lastSongId !== currentSong.id) {
                // Nueva canción o no existe panel → Crear nuevo
                console.log(`🎛️ Creando nuevo panel (${source}): ${currentSong.name}`);
                await this.createNewPanel(channel, queue);
            } else if (source === 'auto_refresh') {
                // Misma canción pero actualización automática → Editar existente
                console.log(`🔄 Actualizando progreso del panel: ${currentSong.name}`);
                await this.updateExistingPanel(guildId, queue);
            }

        } catch (error) {
            console.error('Error creando/actualizando panel:', error);
        }
    }

    /**
     * Crea un nuevo panel de control (garantiza UN SOLO panel por servidor)
     */
    async createNewPanel(channel, queue) {
        try {
            const guildId = channel.guild.id;
            const song = queue.songs[0];

            // GARANTIZAR que se elimine completamente el panel anterior
            await this.removePreviousPanel(guildId);

            // Pequeño delay para asegurar que se eliminó
            await new Promise(resolve => setTimeout(resolve, 100));

            const embed = createNowPlayingEmbed(song, queue);
            const components = createFullControlPanel(queue);

            const message = await channel.send({
                content: '🎛️ **Panel de Control de Música** 🎛️',
                embeds: [embed],
                components: components
            });

            // Guardar referencia del panel con información completa
            this.activePanels.set(guildId, {
                channelId: channel.id,
                messageId: message.id,
                lastUpdate: Date.now(),
                lastSongId: song.id,
                lastProgress: 0, // Progreso inicial
                songStartTime: queue.beginTime || Date.now()
            });

            console.log(`✅ Panel creado exitosamente para: ${song.name}`);
            return message;

        } catch (error) {
            console.error('Error creando nuevo panel:', error);
        }
    }

    /**
     * Actualiza un panel existente de forma inteligente (EDITA el mensaje, no crea uno nuevo)
     */
    async updateExistingPanel(guildId, queue) {
        try {
            const panelInfo = this.activePanels.get(guildId);
            if (!panelInfo) return;

            const guild = this.client.guilds.cache.get(guildId);
            if (!guild) return;

            const channel = guild.channels.cache.get(panelInfo.channelId);
            if (!channel) return;

            // Calcular progreso actual
            const currentSong = queue.songs[0];
            const currentTime = Date.now();
            const songDuration = currentSong.duration * 1000; // Convertir a ms
            const elapsed = currentTime - (queue.beginTime || currentTime);
            const progressPercent = Math.min((elapsed / songDuration) * 100, 100);

            // Solo actualizar si el progreso cambió significativamente
            const lastProgress = panelInfo.lastProgress || 0;
            const progressDiff = Math.abs(progressPercent - lastProgress);

            if (progressDiff < this.PROGRESS_UPDATE_THRESHOLD) {
                // No hay cambio significativo, no actualizar
                return;
            }

            // Obtener el mensaje existente
            const message = await channel.messages.fetch(panelInfo.messageId).catch(() => null);
            if (!message) {
                // Si no se puede obtener el mensaje, crear uno nuevo
                console.log('🔄 Mensaje de panel no encontrado, creando nuevo...');
                await this.createNewPanel(channel, queue);
                return;
            }

            // Crear embed y componentes actualizados
            const embed = createNowPlayingEmbed(currentSong, queue);
            const components = createFullControlPanel(queue);

            // EDITAR el mensaje existente en lugar de crear uno nuevo
            await message.edit({
                content: '🎛️ **Panel de Control de Música** 🎛️',
                embeds: [embed],
                components: components
            });

            // Actualizar información del panel
            panelInfo.lastUpdate = Date.now();
            panelInfo.lastProgress = progressPercent;

            console.log(`🔄 Panel actualizado: ${progressPercent.toFixed(1)}% progreso`);

        } catch (error) {
            console.error('Error actualizando panel existente:', error);
            // Si falla la actualización, crear un panel nuevo como respaldo
            try {
                const guild = this.client.guilds.cache.get(guildId);
                if (guild) {
                    const queue = this.client.distube.getQueue(guild);
                    if (queue && queue.textChannel) {
                        console.log('🔄 Recreando panel debido a error...');
                        await this.createNewPanel(queue.textChannel, queue);
                    }
                }
            } catch (fallbackError) {
                console.error('Error en respaldo de panel:', fallbackError);
                // Como último recurso, eliminar el panel problemático
                this.activePanels.delete(guildId);
            }
        }
    }

    // Función eliminada - ya no usamos panel de información

    /**
     * Elimina el panel anterior de un servidor
     */
    async removePreviousPanel(guildId) {
        try {
            const existingPanel = this.activePanels.get(guildId);
            if (!existingPanel) return;

            const guild = this.client.guilds.cache.get(guildId);
            if (!guild) return;

            const channel = guild.channels.cache.get(existingPanel.channelId);
            if (!channel) return;

            const message = await channel.messages.fetch(existingPanel.messageId).catch(() => null);
            if (message) {
                await message.delete().catch(() => {});
            }

            this.activePanels.delete(guildId);

        } catch (error) {
            console.error('Error eliminando panel anterior:', error);
        }
    }

    // Función eliminada - ahora solo usamos refreshAllPanels

    /**
     * Refresca paneles de forma inteligente y eficiente
     */
    async refreshAllPanels() {
        const startTime = Date.now();
        let panelsUpdated = 0;
        let panelsSkipped = 0;

        for (const [guildId, panelInfo] of this.activePanels.entries()) {
            try {
                const guild = this.client.guilds.cache.get(guildId);
                if (!guild) {
                    this.activePanels.delete(guildId);
                    continue;
                }

                const queue = this.client.distube.getQueue(guild);

                // Si no hay música, eliminar panel
                if (!queue || !queue.songs.length) {
                    await this.removePreviousPanel(guildId);
                    continue;
                }

                const textChannel = guild.channels.cache.get(panelInfo.channelId);
                if (!textChannel) {
                    this.activePanels.delete(guildId);
                    continue;
                }

                // Verificar si necesita actualización
                const currentSong = queue.songs[0];
                const songChanged = panelInfo.lastSongId !== currentSong.id;
                const timeSinceUpdate = Date.now() - panelInfo.lastUpdate;

                if (songChanged) {
                    // Nueva canción - crear panel nuevo
                    await this.createOrUpdatePanel(textChannel, 'song_change');
                    panelsUpdated++;
                } else if (timeSinceUpdate > 30000) {
                    // Actualización de progreso - solo si es necesario
                    await this.createOrUpdatePanel(textChannel, 'auto_refresh');
                    panelsUpdated++;
                } else {
                    // No necesita actualización
                    panelsSkipped++;
                }

                // Delay entre servidores para evitar rate limiting
                await new Promise(resolve => setTimeout(resolve, 200));

            } catch (error) {
                console.error(`Error refrescando panel en servidor ${guildId}:`, error);
                this.activePanels.delete(guildId);
            }
        }

        const totalTime = Date.now() - startTime;
        if (panelsUpdated > 0) {
            console.log(`🎛️ Actualización de paneles: ${panelsUpdated} actualizados, ${panelsSkipped} omitidos en ${totalTime}ms`);
        }
    }

    /**
     * Obtiene información del panel activo en un servidor
     */
    getPanelInfo(guildId) {
        return this.activePanels.get(guildId);
    }

    /**
     * Elimina el panel de un servidor específico
     */
    async removePanel(guildId) {
        await this.removePreviousPanel(guildId);
    }
}

module.exports = PersistentPanel;
