{"name": "@jsep-plugin/assignment", "version": "1.3.0", "description": "Adds assignment expression support", "author": "<PERSON><PERSON> (https://github.com/6utt3rfly)", "maintainers": ["<PERSON> (https://github.com/Eric<PERSON>me<PERSON>s)", "<PERSON> Verou (https://github.com/LeaVerou)", "<PERSON><PERSON> (https://github.com/6utt3rfly)"], "publishConfig": {"access": "public"}, "homepage": "https://ericsmekens.github.io/jsep/tree/master/packages/assignment#readme", "license": "MIT", "repository": {"url": "EricSmekens/jsep", "directory": "packages/assignment"}, "type": "module", "main": "./dist/cjs/index.cjs.js", "module": "./dist/index.js", "types": "types/tsd.d.ts", "peerDependencies": {"jsep": "^0.4.0||^1.0.0"}, "devDependencies": {"rollup": "^2.44.0", "rollup-plugin-delete": "^2.0.0"}, "engines": {"node": ">= 10.16.0"}, "scripts": {"build": "rollup -c ../../plugin.rollup.config.js && cp ../../package-cjs.json dist/cjs/package.json && cp ../../LICENSE ./", "test": "cd ../../ && http-server -p 49649 --silent & node-qunit-puppeteer http://localhost:49649/packages/assignment/test/unit_tests.html", "lint": "eslint src/**/*.js test/**/*.js"}}