{"name": "async.util.keyiterator", "description": "async keyiteratorhelper method as module.", "main": "./index.js", "repository": "async-js/async.util", "author": {"email": "<EMAIL>", "name": "<PERSON><PERSON>", "url": "https://github.com/Kikobeats"}, "version": "0.5.2", "license": "MIT", "dependencies": {"async.util.isarraylike": "0.5.2", "async.util.keys": "0.5.2"}, "keywords": ["async.util", "keyiterator"]}