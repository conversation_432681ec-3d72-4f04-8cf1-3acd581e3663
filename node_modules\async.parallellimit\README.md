# async.parallellimit

![Last version](https://img.shields.io/github/tag/async-js/async.parallellimit.svg?style=flat-square)
[![Dependency status](http://img.shields.io/david/async-js/async.parallellimit.svg?style=flat-square)](https://david-dm.org/async-js/async.parallellimit)
[![Dev Dependencies Status](http://img.shields.io/david/dev/async-js/async.parallellimit.svg?style=flat-square)](https://david-dm.org/async-js/async.parallellimit#info=devDependencies)
[![NPM Status](http://img.shields.io/npm/dm/async.parallellimit.svg?style=flat-square)](https://www.npmjs.org/package/async.parallellimit)
[![Donate](https://img.shields.io/badge/donate-paypal-blue.svg?style=flat-square)](https://paypal.me/kikobeats)

> [async#parallellimit](https://github.com/async-js/async#async.parallellimit) method as module.

## License

MIT © [async-js](https://github.com/async-js)
