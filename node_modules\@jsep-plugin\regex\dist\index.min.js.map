{"version": 3, "file": "index.min.js", "sources": ["../src/index.js"], "sourcesContent": ["const FSLASH_CODE = 47; // '/'\nconst BSLASH_CODE = 92; // '\\\\'\n\nexport default {\n\tname: 'regex',\n\n\tinit(jsep) {\n\t\t// Regex literal: /abc123/ig\n\t\tjsep.hooks.add('gobble-token', function gobbleRegexLiteral(env) {\n\t\t\tif (this.code === FSLASH_CODE) {\n\t\t\t\tconst patternIndex = ++this.index;\n\n\t\t\t\tlet inCharSet = false;\n\t\t\t\twhile (this.index < this.expr.length) {\n\t\t\t\t\tif (this.code === FSLASH_CODE && !inCharSet) {\n\t\t\t\t\t\tconst pattern = this.expr.slice(patternIndex, this.index);\n\n\t\t\t\t\t\tlet flags = '';\n\t\t\t\t\t\twhile (++this.index < this.expr.length) {\n\t\t\t\t\t\t\tconst code = this.code;\n\t\t\t\t\t\t\tif ((code >= 97 && code <= 122) // a...z\n\t\t\t\t\t\t\t\t|| (code >= 65 && code <= 90) // A...Z\n\t\t\t\t\t\t\t\t|| (code >= 48 && code <= 57)) { // 0-9\n\t\t\t\t\t\t\t\tflags += this.char;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tlet value;\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tvalue = new RegExp(pattern, flags);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tcatch (e) {\n\t\t\t\t\t\t\tthis.throwError(e.message);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tenv.node = {\n\t\t\t\t\t\t\ttype: jsep.LITERAL,\n\t\t\t\t\t\t\tvalue,\n\t\t\t\t\t\t\traw: this.expr.slice(patternIndex - 1, this.index),\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\t// allow . [] and () after regex: /regex/.test(a)\n\t\t\t\t\t\tenv.node = this.gobbleTokenProperty(env.node);\n\t\t\t\t\t\treturn env.node;\n\t\t\t\t\t}\n\t\t\t\t\tif (this.code === jsep.OBRACK_CODE) {\n\t\t\t\t\t\tinCharSet = true;\n\t\t\t\t\t}\n\t\t\t\t\telse if (inCharSet && this.code === jsep.CBRACK_CODE) {\n\t\t\t\t\t\tinCharSet = false;\n\t\t\t\t\t}\n\t\t\t\t\tthis.index += this.code === BSLASH_CODE ? 2 : 1;\n\t\t\t\t}\n\t\t\t\tthis.throwError('Unclosed Regex');\n\t\t\t}\n\t\t});\n\t},\n};\n"], "names": ["name", "init", "jsep", "hooks", "add", "env", "this", "code", "patternIndex", "index", "inCharSet", "expr", "length", "pattern", "slice", "value", "flags", "char", "RegExp", "e", "throwError", "message", "node", "type", "LITERAL", "raw", "gobbleTokenProperty", "OBRACK_CODE", "CBRACK_CODE"], "mappings": "AAGA,MAAe,CACdA,KAAM,QAENC,KAAKC,GAEJA,EAAKC,MAAMC,IAAI,gBAAgB,SAA4BC,GAC1D,GATiB,KASbC,KAAKC,KAAsB,CAC9B,MAAMC,IAAiBF,KAAKG,MAE5B,IAAIC,GAAY,EAChB,KAAOJ,KAAKG,MAAQH,KAAKK,KAAKC,QAAQ,CACrC,GAde,KAcXN,KAAKC,OAAyBG,EAAW,CAC5C,MAAMG,EAAUP,KAAKK,KAAKG,MAAMN,EAAcF,KAAKG,OAEnD,IAaIM,EAbAC,EAAQ,GACZ,OAASV,KAAKG,MAAQH,KAAKK,KAAKC,QAAQ,CACvC,MAAML,EAAOD,KAAKC,KAClB,KAAKA,GAAQ,IAAMA,GAAQ,KACtBA,GAAQ,IAAMA,GAAQ,IACtBA,GAAQ,IAAMA,GAAQ,IAI1B,MAHAS,GAASV,KAAKW,KAQhB,IACCF,EAAQ,IAAIG,OAAOL,EAASG,GAE7B,MAAOG,GACNb,KAAKc,WAAWD,EAAEE,SAWnB,OARAhB,EAAIiB,KAAO,CACVC,KAAMrB,EAAKsB,QACXT,MAAAA,EACAU,IAAKnB,KAAKK,KAAKG,MAAMN,EAAe,EAAGF,KAAKG,QAI7CJ,EAAIiB,KAAOhB,KAAKoB,oBAAoBrB,EAAIiB,MACjCjB,EAAIiB,KAERhB,KAAKC,OAASL,EAAKyB,YACtBjB,GAAY,EAEJA,GAAaJ,KAAKC,OAASL,EAAK0B,cACxClB,GAAY,GAEbJ,KAAKG,OArDU,KAqDDH,KAAKC,KAAuB,EAAI,EAE/CD,KAAKc,WAAW"}