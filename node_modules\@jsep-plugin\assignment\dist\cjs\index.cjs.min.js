"use strict";const e={name:"assignment",assignmentOperators:new Set(["=","*=","**=","/=","%=","+=","-=","<<=",">>=",">>>=","&=","^=","|=","||=","&&=","??="]),updateOperators:[43,45],assignmentPrecedence:.9,init(t){const o=[t.IDENTIFIER,t.MEMBER_EXP];function n(t){e.assignmentOperators.has(t.operator)?(t.type="AssignmentExpression",n(t.left),n(t.right)):t.operator||Object.values(t).forEach((e=>{e&&"object"==typeof e&&n(e)}))}e.assignmentOperators.forEach((o=>t.addBinaryOp(o,e.assignmentPrecedence,!0))),t.hooks.add("gobble-token",(function(t){const n=this.code;e.updateOperators.some((e=>e===n&&e===this.expr.charCodeAt(this.index+1)))&&(this.index+=2,t.node={type:"UpdateExpression",operator:43===n?"++":"--",argument:this.gobbleTokenProperty(this.gobbleIdentifier()),prefix:!0},t.node.argument&&o.includes(t.node.argument.type)||this.throwError(`Unexpected ${t.node.operator}`))})),t.hooks.add("after-token",(function(t){if(t.node){const n=this.code;e.updateOperators.some((e=>e===n&&e===this.expr.charCodeAt(this.index+1)))&&(o.includes(t.node.type)||this.throwError(`Unexpected ${t.node.operator}`),this.index+=2,t.node={type:"UpdateExpression",operator:43===n?"++":"--",argument:t.node,prefix:!1})}})),t.hooks.add("after-expression",(function(e){e.node&&n(e.node)}))}};module.exports=e;
//# sourceMappingURL=index.cjs.min.js.map
