[npm]: https://img.shields.io/npm/v/@jsep-plugin/assignment
[npm-url]: https://www.npmjs.com/package/@jsep-plugin/assignment
[size]: https://packagephobia.now.sh/badge?p=@jsep-plugin/assignment
[size-url]: https://packagephobia.now.sh/result?p=@jsep-plugin/assignment

[![npm][npm]][npm-url]
[![size][size]][size-url]

# @jsep-plugin/assignment

A JSEP plugin for adding assignment expression support. Allows expressions of the form:

```javascript
jsep('a = 2');
jsep('a += 2');
jsep('a++');
jsep('--aa');
```

## Install

```console
npm install @jsep-plugin/assignment
# or
yarn add @jsep-plugin/assignment
```

## Usage
```javascript
import jsep from 'jsep';
import jsepAssignment from '@jsep-plugin/assignment';
jsep.plugins.register(jsepAssignment);
```

## Meta

[LICENSE (MIT)](/LICENSE)
