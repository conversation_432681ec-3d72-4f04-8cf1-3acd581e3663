{"name": "async.util.parallel", "description": "async parallelhelper method as module.", "main": "./index.js", "repository": "async-js/async.util", "author": {"email": "<EMAIL>", "name": "<PERSON><PERSON>", "url": "https://github.com/Kikobeats"}, "version": "0.5.2", "license": "MIT", "dependencies": {"async.util.isarraylike": "0.5.2", "async.util.noop": "0.5.2", "async.util.restparam": "0.5.2"}, "keywords": ["async.util", "parallel"]}