{"version": 3, "file": "index.cjs.js", "sources": ["../../src/index.js"], "sourcesContent": ["const PLUS_CODE = 43; // +\nconst MINUS_CODE = 45; // -\n\nconst plugin = {\n\tname: 'assignment',\n\n\tassignmentOperators: new Set([\n\t\t'=',\n\t\t'*=',\n\t\t'**=',\n\t\t'/=',\n\t\t'%=',\n\t\t'+=',\n\t\t'-=',\n\t\t'<<=',\n\t\t'>>=',\n\t\t'>>>=',\n\t\t'&=',\n\t\t'^=',\n\t\t'|=',\n\t\t'||=',\n\t\t'&&=',\n\t\t'??=',\n\t]),\n\tupdateOperators: [PLUS_CODE, MINUS_CODE],\n\tassignmentPrecedence: 0.9,\n\n\tinit(jsep) {\n\t\tconst updateNodeTypes = [jsep.IDENTIFIER, jsep.MEMBER_EXP];\n\t\tplugin.assignmentOperators.forEach(op => jsep.addBinaryOp(op, plugin.assignmentPrecedence, true));\n\n\t\tjsep.hooks.add('gobble-token', function gobbleUpdatePrefix(env) {\n\t\t\tconst code = this.code;\n\t\t\tif (plugin.updateOperators.some(c => c === code && c === this.expr.charCodeAt(this.index + 1))) {\n\t\t\t\tthis.index += 2;\n\t\t\t\tenv.node = {\n\t\t\t\t\ttype: 'UpdateExpression',\n\t\t\t\t\toperator: code === PLUS_CODE ? '++' : '--',\n\t\t\t\t\targument: this.gobbleTokenProperty(this.gobbleIdentifier()),\n\t\t\t\t\tprefix: true,\n\t\t\t\t};\n\t\t\t\tif (!env.node.argument || !updateNodeTypes.includes(env.node.argument.type)) {\n\t\t\t\t\tthis.throwError(`Unexpected ${env.node.operator}`);\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\n\t\tjsep.hooks.add('after-token', function gobbleUpdatePostfix(env) {\n\t\t\tif (env.node) {\n\t\t\t\tconst code = this.code;\n\t\t\t\tif (plugin.updateOperators.some(c => c === code && c === this.expr.charCodeAt(this.index + 1))) {\n\t\t\t\t\tif (!updateNodeTypes.includes(env.node.type)) {\n\t\t\t\t\t\tthis.throwError(`Unexpected ${env.node.operator}`);\n\t\t\t\t\t}\n\t\t\t\t\tthis.index += 2;\n\t\t\t\t\tenv.node = {\n\t\t\t\t\t\ttype: 'UpdateExpression',\n\t\t\t\t\t\toperator: code === PLUS_CODE ? '++' : '--',\n\t\t\t\t\t\targument: env.node,\n\t\t\t\t\t\tprefix: false,\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\n\t\tjsep.hooks.add('after-expression', function gobbleAssignment(env) {\n\t\t\tif (env.node) {\n\t\t\t\t// Note: Binaries can be chained in a single expression to respect\n\t\t\t\t// operator precedence (i.e. a = b = 1 + 2 + 3)\n\t\t\t\t// Update all binary assignment nodes in the tree\n\t\t\t\tupdateBinariesToAssignments(env.node);\n\t\t\t}\n\t\t});\n\n\t\tfunction updateBinariesToAssignments(node) {\n\t\t\tif (plugin.assignmentOperators.has(node.operator)) {\n\t\t\t\tnode.type = 'AssignmentExpression';\n\t\t\t\tupdateBinariesToAssignments(node.left);\n\t\t\t\tupdateBinariesToAssignments(node.right);\n\t\t\t}\n\t\t\telse if (!node.operator) {\n\t\t\t\tObject.values(node).forEach((val) => {\n\t\t\t\t\tif (val && typeof val === 'object') {\n\t\t\t\t\t\tupdateBinariesToAssignments(val);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t},\n};\nexport default plugin;\n"], "names": [], "mappings": ";;AAAA,MAAM,SAAS,GAAG,EAAE,CAAC;AACrB,MAAM,UAAU,GAAG,EAAE,CAAC;AACtB;AACK,MAAC,MAAM,GAAG;AACf,CAAC,IAAI,EAAE,YAAY;AACnB;AACA,CAAC,mBAAmB,EAAE,IAAI,GAAG,CAAC;AAC9B,EAAE,GAAG;AACL,EAAE,IAAI;AACN,EAAE,KAAK;AACP,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,KAAK;AACP,EAAE,KAAK;AACP,EAAE,MAAM;AACR,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,KAAK;AACP,EAAE,KAAK;AACP,EAAE,KAAK;AACP,EAAE,CAAC;AACH,CAAC,eAAe,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC;AACzC,CAAC,oBAAoB,EAAE,GAAG;AAC1B;AACA,CAAC,IAAI,CAAC,IAAI,EAAE;AACZ,EAAE,MAAM,eAAe,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;AAC7D,EAAE,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,IAAI,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,MAAM,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC,CAAC;AACpG;AACA,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,cAAc,EAAE,SAAS,kBAAkB,CAAC,GAAG,EAAE;AAClE,GAAG,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AAC1B,GAAG,IAAI,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE;AACnG,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;AACpB,IAAI,GAAG,CAAC,IAAI,GAAG;AACf,KAAK,IAAI,EAAE,kBAAkB;AAC7B,KAAK,QAAQ,EAAE,IAAI,KAAK,SAAS,GAAG,IAAI,GAAG,IAAI;AAC/C,KAAK,QAAQ,EAAE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAChE,KAAK,MAAM,EAAE,IAAI;AACjB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AACjF,KAAK,IAAI,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AACxD,KAAK;AACL,IAAI;AACJ,GAAG,CAAC,CAAC;AACL;AACA,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,aAAa,EAAE,SAAS,mBAAmB,CAAC,GAAG,EAAE;AAClE,GAAG,IAAI,GAAG,CAAC,IAAI,EAAE;AACjB,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AAC3B,IAAI,IAAI,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE;AACpG,KAAK,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AACnD,MAAM,IAAI,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AACzD,MAAM;AACN,KAAK,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;AACrB,KAAK,GAAG,CAAC,IAAI,GAAG;AAChB,MAAM,IAAI,EAAE,kBAAkB;AAC9B,MAAM,QAAQ,EAAE,IAAI,KAAK,SAAS,GAAG,IAAI,GAAG,IAAI;AAChD,MAAM,QAAQ,EAAE,GAAG,CAAC,IAAI;AACxB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,CAAC;AACP,KAAK;AACL,IAAI;AACJ,GAAG,CAAC,CAAC;AACL;AACA,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,kBAAkB,EAAE,SAAS,gBAAgB,CAAC,GAAG,EAAE;AACpE,GAAG,IAAI,GAAG,CAAC,IAAI,EAAE;AACjB;AACA;AACA;AACA,IAAI,2BAA2B,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC1C,IAAI;AACJ,GAAG,CAAC,CAAC;AACL;AACA,EAAE,SAAS,2BAA2B,CAAC,IAAI,EAAE;AAC7C,GAAG,IAAI,MAAM,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;AACtD,IAAI,IAAI,CAAC,IAAI,GAAG,sBAAsB,CAAC;AACvC,IAAI,2BAA2B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3C,IAAI,2BAA2B,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC5C,IAAI;AACJ,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAC5B,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;AACzC,KAAK,IAAI,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;AACzC,MAAM,2BAA2B,CAAC,GAAG,CAAC,CAAC;AACvC,MAAM;AACN,KAAK,CAAC,CAAC;AACP,IAAI;AACJ,GAAG;AACH,EAAE;AACF;;;;"}