const { SlashCommandBuilder, ActionRowBuilder, StringSelectMenuBuilder } = require('discord.js');

module.exports = {
    name: 'play',
    description: 'Reproduce música de YouTube, Spotify o búsquedas de texto',
    data: new SlashCommandBuilder()
        .setName('play')
        .setDescription('Reproduce música de YouTube, Spotify o búsquedas de texto')
        .addStringOption(option =>
            option.setName('cancion')
                .setDescription('URL o nombre de la canción/playlist')
                .setRequired(true)),
    
    // Comando tradicional (r!play)
    async execute(message, args, client) {
        const query = args.join(' ');
        
        // Eliminar mensaje del usuario
        setTimeout(() => {
            message.delete().catch(() => {});
        }, 1000);
        
        return this.playMusic({
            query,
            voiceChannel: message.member.voice.channel,
            member: message.member,
            channel: message.channel,
            user: message.author,
            client,
            isSlash: false,
            originalMessage: message
        });
    },

    // Slash command (/play)
    async executeSlash(interaction, client) {
        const query = interaction.options.getString('cancion');
        
        return this.playMusic({
            query,
            voiceChannel: interaction.member.voice.channel,
            member: interaction.member,
            channel: interaction.channel,
            user: interaction.user,
            client,
            isSlash: true,
            interaction
        });
    },

    // Función principal con limpieza de URLs
    async playMusic({ query, voiceChannel, member, channel, user, client, isSlash, originalMessage, interaction }) {
        // Verificaciones básicas
        if (!voiceChannel) {
            const message = '❌ ¡Necesitas estar en un canal de voz!';
            if (isSlash) {
                return interaction.reply({ content: message, ephemeral: true });
            } else {
                return channel.send(message);
            }
        }

        if (!query) {
            const message = '❌ ¡Por favor proporciona una canción para reproducir!';
            if (isSlash) {
                return interaction.reply({ content: message, ephemeral: true });
            } else {
                return channel.send(message);
            }
        }

        // Verificar permisos
        const permissions = voiceChannel.permissionsFor(client.user);
        if (!permissions.has('Connect') || !permissions.has('Speak')) {
            const message = '❌ ¡Necesito permisos para unirme y hablar en el canal de voz!';
            if (isSlash) {
                return interaction.reply({ content: message, ephemeral: true });
            } else {
                return channel.send(message);
            }
        }

        try {
            // Detectar si es URL o búsqueda de texto
            const isUrl = query.startsWith('http://') || query.startsWith('https://');
            const isSpotifyUrl = query.includes('spotify.com');

            if (isUrl || isSpotifyUrl) {
                // Es una URL - reproducir directamente
                return this.playDirectUrl(query, voiceChannel, member, channel, user, client, isSlash, originalMessage, interaction);
            } else {
                // Es búsqueda de texto - mostrar menú de selección
                return this.playWithSearch(query, voiceChannel, member, channel, user, client, isSlash, originalMessage, interaction);
            }

        } catch (error) {
            console.error('Error en comando play:', error);

            let errorMessage = '❌ Error al reproducir la música.';

            // Mensajes de error más específicos
            if (error.message.includes('No result found')) {
                errorMessage = '❌ No se encontraron resultados para tu búsqueda.';
            } else if (error.message.includes('private')) {
                errorMessage = '❌ Este contenido es privado o no está disponible.';
            } else if (error.message.includes('age')) {
                errorMessage = '❌ Este contenido tiene restricciones de edad.';
            } else if (error.message.includes('NOT_SUPPORTED_URL')) {
                errorMessage = '❌ Esta URL no es compatible. Intenta con una URL diferente o busca por nombre.';
            } else if (error.message.includes('unavailable')) {
                errorMessage = '❌ Este contenido no está disponible en tu región.';
            }

            if (isSlash) {
                if (interaction.replied || interaction.deferred) {
                    interaction.editReply(errorMessage);
                } else {
                    interaction.reply({ content: errorMessage, ephemeral: true });
                }
            } else {
                channel.send(errorMessage);
            }
        }
    },

    // Función para reproducir URLs directamente
    async playDirectUrl(query, voiceChannel, member, channel, user, client, isSlash, originalMessage, interaction) {
        try {
            // Mostrar mensaje de procesamiento
            if (isSlash) {
                await interaction.deferReply();
                await interaction.editReply('🔍 Procesando URL...');
            } else {
                const searchMsg = await channel.send('🔍 Procesando URL...');
                setTimeout(() => searchMsg.delete().catch(() => {}), 3000);
            }

            console.log(`🎵 DisTube procesando URL: ${query}`);

            // DisTube maneja URLs automáticamente
            await client.distube.play(voiceChannel, query, {
                member,
                textChannel: channel,
                message: isSlash ? null : originalMessage
            });

            console.log(`✅ URL procesada exitosamente: ${query}`);

            // Confirmación para slash commands
            if (isSlash) {
                await interaction.editReply('✅ ¡URL procesada exitosamente!');
            }

        } catch (error) {
            console.error('Error procesando URL:', error);
            throw error; // Re-lanzar para manejo en función principal
        }
    },

    // Función para búsquedas con menú de selección
    async playWithSearch(query, voiceChannel, member, channel, user, client, isSlash, originalMessage, interaction) {
        try {
            // Mostrar mensaje de búsqueda
            if (isSlash) {
                await interaction.deferReply();
                await interaction.editReply('🔍 Buscando canciones...');
            } else {
                const searchMsg = await channel.send('🔍 Buscando canciones...');
                setTimeout(() => searchMsg.delete().catch(() => {}), 30000);
            }

            console.log(`🔍 Buscando: ${query}`);

            // Usar DisTube para buscar (el YouTubePlugin permite búsquedas)
            const searchResults = await client.distube.search(query, {
                limit: 5,
                type: 'video'
            });

            if (!searchResults || searchResults.length === 0) {
                const errorMsg = '❌ No se encontraron resultados para tu búsqueda.';
                if (isSlash) {
                    return interaction.editReply(errorMsg);
                } else {
                    return channel.send(errorMsg);
                }
            }

            // Si solo hay un resultado, reproducir directamente
            if (searchResults.length === 1) {
                console.log(`🎵 Un solo resultado encontrado, reproduciendo: ${searchResults[0].name}`);

                await client.distube.play(voiceChannel, searchResults[0], {
                    member,
                    textChannel: channel,
                    message: isSlash ? null : originalMessage
                });

                if (isSlash) {
                    await interaction.editReply(`✅ Reproduciendo: **${searchResults[0].name}**`);
                }
                return;
            }

            // Múltiples resultados - crear menú de selección
            console.log(`🎵 ${searchResults.length} resultados encontrados, mostrando menú`);
            return this.showSelectionMenu(searchResults, voiceChannel, member, channel, user, client, isSlash, originalMessage, interaction);

        } catch (error) {
            console.error('Error en búsqueda:', error);
            throw error; // Re-lanzar para manejo en función principal
        }
    },

    // Función para mostrar menú de selección
    async showSelectionMenu(searchResults, voiceChannel, member, channel, user, client, isSlash, originalMessage, interaction) {
        try {
            // Crear menú de selección
            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId('song_select')
                .setPlaceholder('🎵 Selecciona una canción...')
                .addOptions(
                    searchResults.map((song, index) => ({
                        label: song.name.length > 100 ? song.name.substring(0, 97) + '...' : song.name,
                        description: `${song.uploader?.name || 'Desconocido'} - ${song.formattedDuration}`,
                        value: index.toString()
                    }))
                );

            const row = new ActionRowBuilder().addComponents(selectMenu);

            let menuMessage;
            if (isSlash) {
                menuMessage = await interaction.editReply({
                    content: '🎵 **Selecciona una canción:**',
                    components: [row]
                });
            } else {
                menuMessage = await channel.send({
                    content: '🎵 **Selecciona una canción:**',
                    components: [row]
                });
            }

            // Crear colector para la selección
            const filter = i => i.customId === 'song_select' && i.user.id === user.id;
            const collector = menuMessage.createMessageComponentCollector({
                filter,
                time: 30000,
                max: 1
            });

            collector.on('collect', async (selectInteraction) => {
                try {
                    const selectedIndex = parseInt(selectInteraction.values[0]);
                    const selectedSong = searchResults[selectedIndex];

                    await selectInteraction.deferUpdate();

                    console.log(`🎵 Usuario seleccionó: ${selectedSong.name}`);

                    // Reproducir la canción seleccionada
                    await client.distube.play(voiceChannel, selectedSong, {
                        member,
                        textChannel: channel,
                        message: isSlash ? null : originalMessage
                    });

                    // Actualizar mensaje
                    if (isSlash) {
                        await interaction.editReply({
                            content: `✅ **Reproduciendo:** ${selectedSong.name}`,
                            components: []
                        });
                    } else {
                        await menuMessage.edit({
                            content: `✅ **Reproduciendo:** ${selectedSong.name}`,
                            components: []
                        });

                        // Eliminar mensaje después de un momento
                        setTimeout(() => {
                            menuMessage.delete().catch(() => {});
                        }, 5000);
                    }

                } catch (error) {
                    console.error('Error reproduciendo canción seleccionada:', error);

                    const errorMsg = '❌ Error reproduciendo la canción seleccionada.';
                    if (isSlash) {
                        await interaction.editReply({ content: errorMsg, components: [] });
                    } else {
                        await menuMessage.edit({ content: errorMsg, components: [] });
                    }
                }
            });

            collector.on('end', collected => {
                if (collected.size === 0) {
                    // Tiempo agotado
                    const timeoutMsg = '⏰ Tiempo agotado. Usa el comando de nuevo para buscar.';

                    if (isSlash) {
                        interaction.editReply({
                            content: timeoutMsg,
                            components: []
                        }).catch(() => {});
                    } else {
                        menuMessage.edit({
                            content: timeoutMsg,
                            components: []
                        }).catch(() => {});

                        // Eliminar mensaje después de un momento
                        setTimeout(() => {
                            menuMessage.delete().catch(() => {});
                        }, 5000);
                    }
                }
            });

        } catch (error) {
            console.error('Error creando menú de selección:', error);
            throw error;

        } catch (error) {
            console.error('Error en comando play:', error);

            let errorMessage = '❌ Error al reproducir la música.';

            // Mensajes de error más específicos
            if (error.message.includes('No result found')) {
                errorMessage = '❌ No se encontraron resultados para tu búsqueda.';
            } else if (error.message.includes('private')) {
                errorMessage = '❌ Este contenido es privado o no está disponible.';
            } else if (error.message.includes('age')) {
                errorMessage = '❌ Este contenido tiene restricciones de edad.';
            } else if (error.message.includes('NOT_SUPPORTED_URL')) {
                errorMessage = '❌ Esta URL no es compatible. Intenta con una URL diferente o busca por nombre.';
            } else if (error.message.includes('unavailable')) {
                errorMessage = '❌ Este contenido no está disponible en tu región.';
            }

            if (isSlash) {
                if (interaction.replied || interaction.deferred) {
                    interaction.editReply(errorMessage);
                } else {
                    interaction.reply({ content: errorMessage, ephemeral: true });
                }
            } else {
                channel.send(errorMessage);
            }
        }
    }
};
