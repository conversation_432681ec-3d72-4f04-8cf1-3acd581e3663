const fs = require('fs');
const path = require('path');

module.exports = (client) => {
    const eventsPath = path.join(__dirname, '../events');

    // Solo cargar eventos de client, NO los de DisTube (se manejan en index.js)
    const clientEventsPath = path.join(eventsPath, 'client');

    if (fs.existsSync(clientEventsPath)) {
        const eventFiles = fs.readdirSync(clientEventsPath).filter(file => file.endsWith('.js'));

        for (const file of eventFiles) {
            const filePath = path.join(clientEventsPath, file);
            const event = require(filePath);
            const eventName = file.split('.')[0];
            client.on(eventName, (...args) => event(client, ...args));
            console.log(`Evento de client cargado: ${eventName}`);
        }
    }

    // Cargar eventos de DisTube solo si existen y no están ya manejados en index.js
    const distubeEventsPath = path.join(eventsPath, 'distube');

    if (fs.existsSync(distubeEventsPath)) {
        const distubeFiles = fs.readdirSync(distubeEventsPath).filter(file => file.endsWith('.js'));

        for (const file of distubeFiles) {
            const filePath = path.join(distubeEventsPath, file);
            const eventHandler = require(filePath);

            // Solo cargar eventos que NO estén ya manejados en index.js
            if (file === 'debug.js' || file === 'error.js') {
                eventHandler(client);
                console.log(`Evento de DisTube cargado: ${file}`);
            } else {
                console.log(`Evento de DisTube omitido (manejado en index.js): ${file}`);
            }
        }
    }
};
