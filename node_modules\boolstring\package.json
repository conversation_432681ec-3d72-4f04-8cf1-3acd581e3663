{"name": "boolstring", "version": "2.0.1", "description": "Converts a string like true, on, or enabled into a boolean.", "main": "index.js", "module": "index.mjs", "types": "index.d.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/domkalan/boolString.git"}, "keywords": ["bool", "string", "eval"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/domkalan/boolString/issues"}, "homepage": "https://github.com/domkalan/boolString#readme", "devDependencies": {"typescript": "^5.5.4"}}