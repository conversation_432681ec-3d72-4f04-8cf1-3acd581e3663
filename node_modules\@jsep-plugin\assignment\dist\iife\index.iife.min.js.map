{"version": 3, "file": "index.iife.min.js", "sources": ["../../src/index.js"], "sourcesContent": ["const PLUS_CODE = 43; // +\nconst MINUS_CODE = 45; // -\n\nconst plugin = {\n\tname: 'assignment',\n\n\tassignmentOperators: new Set([\n\t\t'=',\n\t\t'*=',\n\t\t'**=',\n\t\t'/=',\n\t\t'%=',\n\t\t'+=',\n\t\t'-=',\n\t\t'<<=',\n\t\t'>>=',\n\t\t'>>>=',\n\t\t'&=',\n\t\t'^=',\n\t\t'|=',\n\t\t'||=',\n\t\t'&&=',\n\t\t'??=',\n\t]),\n\tupdateOperators: [PLUS_CODE, MINUS_CODE],\n\tassignmentPrecedence: 0.9,\n\n\tinit(jsep) {\n\t\tconst updateNodeTypes = [jsep.IDENTIFIER, jsep.MEMBER_EXP];\n\t\tplugin.assignmentOperators.forEach(op => jsep.addBinaryOp(op, plugin.assignmentPrecedence, true));\n\n\t\tjsep.hooks.add('gobble-token', function gobbleUpdatePrefix(env) {\n\t\t\tconst code = this.code;\n\t\t\tif (plugin.updateOperators.some(c => c === code && c === this.expr.charCodeAt(this.index + 1))) {\n\t\t\t\tthis.index += 2;\n\t\t\t\tenv.node = {\n\t\t\t\t\ttype: 'UpdateExpression',\n\t\t\t\t\toperator: code === PLUS_CODE ? '++' : '--',\n\t\t\t\t\targument: this.gobbleTokenProperty(this.gobbleIdentifier()),\n\t\t\t\t\tprefix: true,\n\t\t\t\t};\n\t\t\t\tif (!env.node.argument || !updateNodeTypes.includes(env.node.argument.type)) {\n\t\t\t\t\tthis.throwError(`Unexpected ${env.node.operator}`);\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\n\t\tjsep.hooks.add('after-token', function gobbleUpdatePostfix(env) {\n\t\t\tif (env.node) {\n\t\t\t\tconst code = this.code;\n\t\t\t\tif (plugin.updateOperators.some(c => c === code && c === this.expr.charCodeAt(this.index + 1))) {\n\t\t\t\t\tif (!updateNodeTypes.includes(env.node.type)) {\n\t\t\t\t\t\tthis.throwError(`Unexpected ${env.node.operator}`);\n\t\t\t\t\t}\n\t\t\t\t\tthis.index += 2;\n\t\t\t\t\tenv.node = {\n\t\t\t\t\t\ttype: 'UpdateExpression',\n\t\t\t\t\t\toperator: code === PLUS_CODE ? '++' : '--',\n\t\t\t\t\t\targument: env.node,\n\t\t\t\t\t\tprefix: false,\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\n\t\tjsep.hooks.add('after-expression', function gobbleAssignment(env) {\n\t\t\tif (env.node) {\n\t\t\t\t// Note: Binaries can be chained in a single expression to respect\n\t\t\t\t// operator precedence (i.e. a = b = 1 + 2 + 3)\n\t\t\t\t// Update all binary assignment nodes in the tree\n\t\t\t\tupdateBinariesToAssignments(env.node);\n\t\t\t}\n\t\t});\n\n\t\tfunction updateBinariesToAssignments(node) {\n\t\t\tif (plugin.assignmentOperators.has(node.operator)) {\n\t\t\t\tnode.type = 'AssignmentExpression';\n\t\t\t\tupdateBinariesToAssignments(node.left);\n\t\t\t\tupdateBinariesToAssignments(node.right);\n\t\t\t}\n\t\t\telse if (!node.operator) {\n\t\t\t\tObject.values(node).forEach((val) => {\n\t\t\t\t\tif (val && typeof val === 'object') {\n\t\t\t\t\t\tupdateBinariesToAssignments(val);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t},\n};\nexport default plugin;\n"], "names": ["plugin", "name", "assignmentOperators", "Set", "updateOperators", "assignmentPrecedence", "init", "jsep", "updateNodeTypes", "IDENTIFIER", "MEMBER_EXP", "updateBinariesToAssignments", "node", "has", "operator", "type", "left", "right", "Object", "values", "for<PERSON>ach", "val", "op", "addBinaryOp", "hooks", "add", "env", "code", "this", "some", "c", "expr", "charCodeAt", "index", "argument", "gobbleTokenProperty", "gobbleIdentifier", "prefix", "includes", "throwError"], "mappings": "kCAAA,MAGMA,EAAS,CACdC,KAAM,aAENC,oBAAqB,IAAIC,IAAI,CAC5B,IACA,KACA,MACA,KACA,KACA,KACA,KACA,MACA,MACA,OACA,KACA,KACA,KACA,MACA,MACA,QAEDC,gBAAiB,CAxBA,GACC,IAwBlBC,qBAAsB,GAEtBC,KAAKC,GACJ,MAAMC,EAAkB,CAACD,EAAKE,WAAYF,EAAKG,YA8C/C,SAASC,EAA4BC,GAChCZ,EAAOE,oBAAoBW,IAAID,EAAKE,WACvCF,EAAKG,KAAO,uBACZJ,EAA4BC,EAAKI,MACjCL,EAA4BC,EAAKK,QAExBL,EAAKE,UACdI,OAAOC,OAAOP,GAAMQ,SAASC,IACxBA,GAAsB,iBAARA,GACjBV,EAA4BU,MAtDhCrB,EAAOE,oBAAoBkB,SAAQE,GAAMf,EAAKgB,YAAYD,EAAItB,EAAOK,sBAAsB,KAE3FE,EAAKiB,MAAMC,IAAI,gBAAgB,SAA4BC,GAC1D,MAAMC,EAAOC,KAAKD,KACd3B,EAAOI,gBAAgByB,MAAKC,GAAKA,IAAMH,GAAQG,IAAMF,KAAKG,KAAKC,WAAWJ,KAAKK,MAAQ,OAC1FL,KAAKK,OAAS,EACdP,EAAId,KAAO,CACVG,KAAM,mBACND,SArCa,KAqCHa,EAAqB,KAAO,KACtCO,SAAUN,KAAKO,oBAAoBP,KAAKQ,oBACxCC,QAAQ,GAEJX,EAAId,KAAKsB,UAAa1B,EAAgB8B,SAASZ,EAAId,KAAKsB,SAASnB,OACrEa,KAAKW,WAAW,cAAcb,EAAId,KAAKE,gBAK1CP,EAAKiB,MAAMC,IAAI,eAAe,SAA6BC,GAC1D,GAAIA,EAAId,KAAM,CACb,MAAMe,EAAOC,KAAKD,KACd3B,EAAOI,gBAAgByB,MAAKC,GAAKA,IAAMH,GAAQG,IAAMF,KAAKG,KAAKC,WAAWJ,KAAKK,MAAQ,OACrFzB,EAAgB8B,SAASZ,EAAId,KAAKG,OACtCa,KAAKW,WAAW,cAAcb,EAAId,KAAKE,YAExCc,KAAKK,OAAS,EACdP,EAAId,KAAO,CACVG,KAAM,mBACND,SAzDY,KAyDFa,EAAqB,KAAO,KACtCO,SAAUR,EAAId,KACdyB,QAAQ,QAMZ9B,EAAKiB,MAAMC,IAAI,oBAAoB,SAA0BC,GACxDA,EAAId,MAIPD,EAA4Be,EAAId"}