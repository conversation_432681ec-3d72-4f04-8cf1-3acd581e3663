[npm]: https://img.shields.io/npm/v/@jsep-plugin/regex
[npm-url]: https://www.npmjs.com/package/@jsep-plugin/regex
[size]: https://packagephobia.now.sh/badge?p=@jsep-plugin/regex
[size-url]: https://packagephobia.now.sh/result?p=@jsep-plugin/regex

[![npm][npm]][npm-url]
[![size][size]][size-url]

# @jsep-plugin/regex

A JSEP plugin for adding regex expression support. Allows expressions of the form:

```javascript
jsep('/abc/');
jsep('/abc/ig');
jsep('/[a-z]{3}/ig.test(a)');
```

## Install

```console
npm install @jsep-plugin/regex
# or
yarn add @jsep-plugin/regex
```

## Usage
```javascript
import jsep from 'jsep';
import jsepRegex from '@jsep-plugin/regex';
jsep.plugins.register(jsepRegex);
```

## Meta

[LICENSE (MIT)](/LICENSE)
