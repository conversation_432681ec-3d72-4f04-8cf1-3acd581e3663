{"version": 3, "file": "index.cjs.js", "sources": ["../../src/index.js"], "sourcesContent": ["const FSLASH_CODE = 47; // '/'\nconst BSLASH_CODE = 92; // '\\\\'\n\nexport default {\n\tname: 'regex',\n\n\tinit(jsep) {\n\t\t// Regex literal: /abc123/ig\n\t\tjsep.hooks.add('gobble-token', function gobbleRegexLiteral(env) {\n\t\t\tif (this.code === FSLASH_CODE) {\n\t\t\t\tconst patternIndex = ++this.index;\n\n\t\t\t\tlet inCharSet = false;\n\t\t\t\twhile (this.index < this.expr.length) {\n\t\t\t\t\tif (this.code === FSLASH_CODE && !inCharSet) {\n\t\t\t\t\t\tconst pattern = this.expr.slice(patternIndex, this.index);\n\n\t\t\t\t\t\tlet flags = '';\n\t\t\t\t\t\twhile (++this.index < this.expr.length) {\n\t\t\t\t\t\t\tconst code = this.code;\n\t\t\t\t\t\t\tif ((code >= 97 && code <= 122) // a...z\n\t\t\t\t\t\t\t\t|| (code >= 65 && code <= 90) // A...Z\n\t\t\t\t\t\t\t\t|| (code >= 48 && code <= 57)) { // 0-9\n\t\t\t\t\t\t\t\tflags += this.char;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tlet value;\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tvalue = new RegExp(pattern, flags);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tcatch (e) {\n\t\t\t\t\t\t\tthis.throwError(e.message);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tenv.node = {\n\t\t\t\t\t\t\ttype: jsep.LITERAL,\n\t\t\t\t\t\t\tvalue,\n\t\t\t\t\t\t\traw: this.expr.slice(patternIndex - 1, this.index),\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\t// allow . [] and () after regex: /regex/.test(a)\n\t\t\t\t\t\tenv.node = this.gobbleTokenProperty(env.node);\n\t\t\t\t\t\treturn env.node;\n\t\t\t\t\t}\n\t\t\t\t\tif (this.code === jsep.OBRACK_CODE) {\n\t\t\t\t\t\tinCharSet = true;\n\t\t\t\t\t}\n\t\t\t\t\telse if (inCharSet && this.code === jsep.CBRACK_CODE) {\n\t\t\t\t\t\tinCharSet = false;\n\t\t\t\t\t}\n\t\t\t\t\tthis.index += this.code === BSLASH_CODE ? 2 : 1;\n\t\t\t\t}\n\t\t\t\tthis.throwError('Unclosed Regex');\n\t\t\t}\n\t\t});\n\t},\n};\n"], "names": [], "mappings": ";;AAAA,MAAM,WAAW,GAAG,EAAE,CAAC;AACvB,MAAM,WAAW,GAAG,EAAE,CAAC;AACvB;AACA,YAAe;AACf,CAAC,IAAI,EAAE,OAAO;AACd;AACA,CAAC,IAAI,CAAC,IAAI,EAAE;AACZ;AACA,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,cAAc,EAAE,SAAS,kBAAkB,CAAC,GAAG,EAAE;AAClE,GAAG,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW,EAAE;AAClC,IAAI,MAAM,YAAY,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC;AACtC;AACA,IAAI,IAAI,SAAS,GAAG,KAAK,CAAC;AAC1B,IAAI,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AAC1C,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW,IAAI,CAAC,SAAS,EAAE;AAClD,MAAM,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AAChE;AACA,MAAM,IAAI,KAAK,GAAG,EAAE,CAAC;AACrB,MAAM,OAAO,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AAC9C,OAAO,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AAC9B,OAAO,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,GAAG;AACrC,YAAY,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,EAAE,CAAC;AACrC,YAAY,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,EAAE,CAAC,EAAE;AACvC,QAAQ,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC;AAC3B,QAAQ;AACR,YAAY;AACZ,QAAQ,MAAM;AACd,QAAQ;AACR,OAAO;AACP;AACA,MAAM,IAAI,KAAK,CAAC;AAChB,MAAM,IAAI;AACV,OAAO,KAAK,GAAG,IAAI,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AAC1C,OAAO;AACP,MAAM,OAAO,CAAC,EAAE;AAChB,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;AAClC,OAAO;AACP;AACA,MAAM,GAAG,CAAC,IAAI,GAAG;AACjB,OAAO,IAAI,EAAE,IAAI,CAAC,OAAO;AACzB,OAAO,KAAK;AACZ,OAAO,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC;AACzD,OAAO,CAAC;AACR;AACA;AACA,MAAM,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACpD,MAAM,OAAO,GAAG,CAAC,IAAI,CAAC;AACtB,MAAM;AACN,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,WAAW,EAAE;AACzC,MAAM,SAAS,GAAG,IAAI,CAAC;AACvB,MAAM;AACN,UAAU,IAAI,SAAS,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,WAAW,EAAE;AAC3D,MAAM,SAAS,GAAG,KAAK,CAAC;AACxB,MAAM;AACN,KAAK,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC;AACrD,KAAK;AACL,IAAI,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;AACtC,IAAI;AACJ,GAAG,CAAC,CAAC;AACL,EAAE;AACF,CAAC;;;;"}