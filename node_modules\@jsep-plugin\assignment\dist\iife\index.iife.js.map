{"version": 3, "file": "index.iife.js", "sources": ["../../src/index.js"], "sourcesContent": ["const PLUS_CODE = 43; // +\nconst MINUS_CODE = 45; // -\n\nconst plugin = {\n\tname: 'assignment',\n\n\tassignmentOperators: new Set([\n\t\t'=',\n\t\t'*=',\n\t\t'**=',\n\t\t'/=',\n\t\t'%=',\n\t\t'+=',\n\t\t'-=',\n\t\t'<<=',\n\t\t'>>=',\n\t\t'>>>=',\n\t\t'&=',\n\t\t'^=',\n\t\t'|=',\n\t\t'||=',\n\t\t'&&=',\n\t\t'??=',\n\t]),\n\tupdateOperators: [PLUS_CODE, MINUS_CODE],\n\tassignmentPrecedence: 0.9,\n\n\tinit(jsep) {\n\t\tconst updateNodeTypes = [jsep.IDENTIFIER, jsep.MEMBER_EXP];\n\t\tplugin.assignmentOperators.forEach(op => jsep.addBinaryOp(op, plugin.assignmentPrecedence, true));\n\n\t\tjsep.hooks.add('gobble-token', function gobbleUpdatePrefix(env) {\n\t\t\tconst code = this.code;\n\t\t\tif (plugin.updateOperators.some(c => c === code && c === this.expr.charCodeAt(this.index + 1))) {\n\t\t\t\tthis.index += 2;\n\t\t\t\tenv.node = {\n\t\t\t\t\ttype: 'UpdateExpression',\n\t\t\t\t\toperator: code === PLUS_CODE ? '++' : '--',\n\t\t\t\t\targument: this.gobbleTokenProperty(this.gobbleIdentifier()),\n\t\t\t\t\tprefix: true,\n\t\t\t\t};\n\t\t\t\tif (!env.node.argument || !updateNodeTypes.includes(env.node.argument.type)) {\n\t\t\t\t\tthis.throwError(`Unexpected ${env.node.operator}`);\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\n\t\tjsep.hooks.add('after-token', function gobbleUpdatePostfix(env) {\n\t\t\tif (env.node) {\n\t\t\t\tconst code = this.code;\n\t\t\t\tif (plugin.updateOperators.some(c => c === code && c === this.expr.charCodeAt(this.index + 1))) {\n\t\t\t\t\tif (!updateNodeTypes.includes(env.node.type)) {\n\t\t\t\t\t\tthis.throwError(`Unexpected ${env.node.operator}`);\n\t\t\t\t\t}\n\t\t\t\t\tthis.index += 2;\n\t\t\t\t\tenv.node = {\n\t\t\t\t\t\ttype: 'UpdateExpression',\n\t\t\t\t\t\toperator: code === PLUS_CODE ? '++' : '--',\n\t\t\t\t\t\targument: env.node,\n\t\t\t\t\t\tprefix: false,\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\n\t\tjsep.hooks.add('after-expression', function gobbleAssignment(env) {\n\t\t\tif (env.node) {\n\t\t\t\t// Note: Binaries can be chained in a single expression to respect\n\t\t\t\t// operator precedence (i.e. a = b = 1 + 2 + 3)\n\t\t\t\t// Update all binary assignment nodes in the tree\n\t\t\t\tupdateBinariesToAssignments(env.node);\n\t\t\t}\n\t\t});\n\n\t\tfunction updateBinariesToAssignments(node) {\n\t\t\tif (plugin.assignmentOperators.has(node.operator)) {\n\t\t\t\tnode.type = 'AssignmentExpression';\n\t\t\t\tupdateBinariesToAssignments(node.left);\n\t\t\t\tupdateBinariesToAssignments(node.right);\n\t\t\t}\n\t\t\telse if (!node.operator) {\n\t\t\t\tObject.values(node).forEach((val) => {\n\t\t\t\t\tif (val && typeof val === 'object') {\n\t\t\t\t\t\tupdateBinariesToAssignments(val);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t},\n};\nexport default plugin;\n"], "names": [], "mappings": ";;;CAAA,MAAM,SAAS,GAAG,EAAE,CAAC;CACrB,MAAM,UAAU,GAAG,EAAE,CAAC;AACtB;AACK,OAAC,MAAM,GAAG;CACf,CAAC,IAAI,EAAE,YAAY;AACnB;CACA,CAAC,mBAAmB,EAAE,IAAI,GAAG,CAAC;CAC9B,EAAE,GAAG;CACL,EAAE,IAAI;CACN,EAAE,KAAK;CACP,EAAE,IAAI;CACN,EAAE,IAAI;CACN,EAAE,IAAI;CACN,EAAE,IAAI;CACN,EAAE,KAAK;CACP,EAAE,KAAK;CACP,EAAE,MAAM;CACR,EAAE,IAAI;CACN,EAAE,IAAI;CACN,EAAE,IAAI;CACN,EAAE,KAAK;CACP,EAAE,KAAK;CACP,EAAE,KAAK;CACP,EAAE,CAAC;CACH,CAAC,eAAe,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC;CACzC,CAAC,oBAAoB,EAAE,GAAG;AAC1B;CACA,CAAC,IAAI,CAAC,IAAI,EAAE;CACZ,EAAE,MAAM,eAAe,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;CAC7D,EAAE,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,IAAI,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,MAAM,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC,CAAC;AACpG;CACA,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,cAAc,EAAE,SAAS,kBAAkB,CAAC,GAAG,EAAE;CAClE,GAAG,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;CAC1B,GAAG,IAAI,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE;CACnG,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;CACpB,IAAI,GAAG,CAAC,IAAI,GAAG;CACf,KAAK,IAAI,EAAE,kBAAkB;CAC7B,KAAK,QAAQ,EAAE,IAAI,KAAK,SAAS,GAAG,IAAI,GAAG,IAAI;CAC/C,KAAK,QAAQ,EAAE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;CAChE,KAAK,MAAM,EAAE,IAAI;CACjB,KAAK,CAAC;CACN,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;CACjF,KAAK,IAAI,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;CACxD,KAAK;CACL,IAAI;CACJ,GAAG,CAAC,CAAC;AACL;CACA,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,aAAa,EAAE,SAAS,mBAAmB,CAAC,GAAG,EAAE;CAClE,GAAG,IAAI,GAAG,CAAC,IAAI,EAAE;CACjB,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;CAC3B,IAAI,IAAI,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE;CACpG,KAAK,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;CACnD,MAAM,IAAI,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;CACzD,MAAM;CACN,KAAK,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;CACrB,KAAK,GAAG,CAAC,IAAI,GAAG;CAChB,MAAM,IAAI,EAAE,kBAAkB;CAC9B,MAAM,QAAQ,EAAE,IAAI,KAAK,SAAS,GAAG,IAAI,GAAG,IAAI;CAChD,MAAM,QAAQ,EAAE,GAAG,CAAC,IAAI;CACxB,MAAM,MAAM,EAAE,KAAK;CACnB,MAAM,CAAC;CACP,KAAK;CACL,IAAI;CACJ,GAAG,CAAC,CAAC;AACL;CACA,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,kBAAkB,EAAE,SAAS,gBAAgB,CAAC,GAAG,EAAE;CACpE,GAAG,IAAI,GAAG,CAAC,IAAI,EAAE;CACjB;CACA;CACA;CACA,IAAI,2BAA2B,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;CAC1C,IAAI;CACJ,GAAG,CAAC,CAAC;AACL;CACA,EAAE,SAAS,2BAA2B,CAAC,IAAI,EAAE;CAC7C,GAAG,IAAI,MAAM,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;CACtD,IAAI,IAAI,CAAC,IAAI,GAAG,sBAAsB,CAAC;CACvC,IAAI,2BAA2B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;CAC3C,IAAI,2BAA2B,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;CAC5C,IAAI;CACJ,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;CAC5B,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;CACzC,KAAK,IAAI,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;CACzC,MAAM,2BAA2B,CAAC,GAAG,CAAC,CAAC;CACvC,MAAM;CACN,KAAK,CAAC,CAAC;CACP,IAAI;CACJ,GAAG;CACH,EAAE;CACF;;;;;;;;"}