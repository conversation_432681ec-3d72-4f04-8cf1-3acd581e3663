var e={name:"regex",init(e){e.hooks.add("gobble-token",(function(t){if(47===this.code){const i=++this.index;let s=!1;for(;this.index<this.expr.length;){if(47===this.code&&!s){const s=this.expr.slice(i,this.index);let o,h="";for(;++this.index<this.expr.length;){const e=this.code;if(!(e>=97&&e<=122||e>=65&&e<=90||e>=48&&e<=57))break;h+=this.char}try{o=new RegExp(s,h)}catch(e){this.throwError(e.message)}return t.node={type:e.LITERAL,value:o,raw:this.expr.slice(i-1,this.index)},t.node=this.gobbleTokenProperty(t.node),t.node}this.code===e.OBRACK_CODE?s=!0:s&&this.code===e.CBRACK_CODE&&(s=!1),this.index+=92===this.code?2:1}this.throwError("Unclosed Regex")}}))}};export{e as default};
//# sourceMappingURL=index.min.js.map
