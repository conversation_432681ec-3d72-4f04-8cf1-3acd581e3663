{"version": 3, "file": "index.iife.js", "sources": ["../../src/index.js"], "sourcesContent": ["const FSLASH_CODE = 47; // '/'\nconst BSLASH_CODE = 92; // '\\\\'\n\nexport default {\n\tname: 'regex',\n\n\tinit(jsep) {\n\t\t// Regex literal: /abc123/ig\n\t\tjsep.hooks.add('gobble-token', function gobbleRegexLiteral(env) {\n\t\t\tif (this.code === FSLASH_CODE) {\n\t\t\t\tconst patternIndex = ++this.index;\n\n\t\t\t\tlet inCharSet = false;\n\t\t\t\twhile (this.index < this.expr.length) {\n\t\t\t\t\tif (this.code === FSLASH_CODE && !inCharSet) {\n\t\t\t\t\t\tconst pattern = this.expr.slice(patternIndex, this.index);\n\n\t\t\t\t\t\tlet flags = '';\n\t\t\t\t\t\twhile (++this.index < this.expr.length) {\n\t\t\t\t\t\t\tconst code = this.code;\n\t\t\t\t\t\t\tif ((code >= 97 && code <= 122) // a...z\n\t\t\t\t\t\t\t\t|| (code >= 65 && code <= 90) // A...Z\n\t\t\t\t\t\t\t\t|| (code >= 48 && code <= 57)) { // 0-9\n\t\t\t\t\t\t\t\tflags += this.char;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tlet value;\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tvalue = new RegExp(pattern, flags);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tcatch (e) {\n\t\t\t\t\t\t\tthis.throwError(e.message);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tenv.node = {\n\t\t\t\t\t\t\ttype: jsep.LITERAL,\n\t\t\t\t\t\t\tvalue,\n\t\t\t\t\t\t\traw: this.expr.slice(patternIndex - 1, this.index),\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\t// allow . [] and () after regex: /regex/.test(a)\n\t\t\t\t\t\tenv.node = this.gobbleTokenProperty(env.node);\n\t\t\t\t\t\treturn env.node;\n\t\t\t\t\t}\n\t\t\t\t\tif (this.code === jsep.OBRACK_CODE) {\n\t\t\t\t\t\tinCharSet = true;\n\t\t\t\t\t}\n\t\t\t\t\telse if (inCharSet && this.code === jsep.CBRACK_CODE) {\n\t\t\t\t\t\tinCharSet = false;\n\t\t\t\t\t}\n\t\t\t\t\tthis.index += this.code === BSLASH_CODE ? 2 : 1;\n\t\t\t\t}\n\t\t\t\tthis.throwError('Unclosed Regex');\n\t\t\t}\n\t\t});\n\t},\n};\n"], "names": [], "mappings": ";;;CAAA,MAAM,WAAW,GAAG,EAAE,CAAC;CACvB,MAAM,WAAW,GAAG,EAAE,CAAC;AACvB;AACA,aAAe;CACf,CAAC,IAAI,EAAE,OAAO;AACd;CACA,CAAC,IAAI,CAAC,IAAI,EAAE;CACZ;CACA,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,cAAc,EAAE,SAAS,kBAAkB,CAAC,GAAG,EAAE;CAClE,GAAG,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW,EAAE;CAClC,IAAI,MAAM,YAAY,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC;AACtC;CACA,IAAI,IAAI,SAAS,GAAG,KAAK,CAAC;CAC1B,IAAI,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;CAC1C,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW,IAAI,CAAC,SAAS,EAAE;CAClD,MAAM,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AAChE;CACA,MAAM,IAAI,KAAK,GAAG,EAAE,CAAC;CACrB,MAAM,OAAO,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;CAC9C,OAAO,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;CAC9B,OAAO,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,GAAG;CACrC,YAAY,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,EAAE,CAAC;CACrC,YAAY,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,EAAE,CAAC,EAAE;CACvC,QAAQ,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC;CAC3B,QAAQ;CACR,YAAY;CACZ,QAAQ,MAAM;CACd,QAAQ;CACR,OAAO;AACP;CACA,MAAM,IAAI,KAAK,CAAC;CAChB,MAAM,IAAI;CACV,OAAO,KAAK,GAAG,IAAI,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;CAC1C,OAAO;CACP,MAAM,OAAO,CAAC,EAAE;CAChB,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;CAClC,OAAO;AACP;CACA,MAAM,GAAG,CAAC,IAAI,GAAG;CACjB,OAAO,IAAI,EAAE,IAAI,CAAC,OAAO;CACzB,OAAO,KAAK;CACZ,OAAO,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC;CACzD,OAAO,CAAC;AACR;CACA;CACA,MAAM,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;CACpD,MAAM,OAAO,GAAG,CAAC,IAAI,CAAC;CACtB,MAAM;CACN,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,WAAW,EAAE;CACzC,MAAM,SAAS,GAAG,IAAI,CAAC;CACvB,MAAM;CACN,UAAU,IAAI,SAAS,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,WAAW,EAAE;CAC3D,MAAM,SAAS,GAAG,KAAK,CAAC;CACxB,MAAM;CACN,KAAK,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC;CACrD,KAAK;CACL,IAAI,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;CACtC,IAAI;CACJ,GAAG,CAAC,CAAC;CACL,EAAE;CACF,CAAC;;;;;;;;"}